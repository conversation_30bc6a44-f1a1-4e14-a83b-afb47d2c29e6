# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Data loading module for the Terrafloww SDK.

This module provides functions to load geospatial data into Workflow objects.
These objects represent lazy data streams that can be transformed using the apply methods
and materialized using compute().
"""

import logging
import os
import asyncio
from typing import Any, Dict, List, Optional, Union

import geopandas as gpd
from shapely.geometry.base import BaseGeometry
from shapely.wkt import loads as wkt_loads

# Internal SDK Components
from .workflow import Workflow

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Default backend service addresses
DEFAULT_PROCESSING_GRPC_TARGET = os.environ.get("TFW_PROCESSING_GRPC_TARGET", "localhost:50051")
DEFAULT_PROCESSING_FLIGHT_TARGET = os.environ.get("TFW_PROCESSING_FLIGHT_TARGET", "grpc+tcp://localhost:50052")


async def _load_async(
    collection: str,
    bands: Optional[List[str]] = None,
    bbox: Optional[List[float]] = None,
    aoi: Optional[Union[BaseGeometry, gpd.GeoDataFrame, gpd.GeoSeries, str]] = None,
    datetime: Optional[str] = None,
    filters: Optional[Dict[str, Any]] = None,
    limit: Optional[int] = None,
    processing_engine_grpc_target: Optional[str] = None,
    processing_engine_flight_target: Optional[str] = None,
) -> 'Workflow':
    """
    Asynchronous internal function to create a lazy Workflow.
    
    Args:
        collection: Name or ID of the geospatial data collection
        bands: List of band names to select
        bbox: Bounding box as [west, south, east, north] in EPSG:4326
        aoi: Area of interest as Shapely geometry, WKT string, or GeoPandas object
        datetime: Time range or specific datetime (e.g., "2020-01-01/2020-12-31")
        filters: Additional filters for data selection
        limit: Maximum number of scenes to process
        processing_engine_grpc_target: gRPC endpoint for the processing engine
        processing_engine_flight_target: Arrow Flight endpoint for the processing engine
    
    Returns:
        A lazy Workflow representing the data loading operation
    """
    logger.info(f"Defining lazy load request for collection: '{collection}'")

    # Resolve backend targets
    final_grpc_target = processing_engine_grpc_target or DEFAULT_PROCESSING_GRPC_TARGET
    final_flight_target = processing_engine_flight_target or DEFAULT_PROCESSING_FLIGHT_TARGET

    # Process AOI - prioritize bbox if both bbox and aoi are provided
    aoi_wkt_str = None
    aoi_crs_str = None
    
    if bbox is not None:
        # Convert bbox to WKT polygon
        if len(bbox) != 4:
            raise ValueError("bbox must be [west, south, east, north]")
        west, south, east, north = bbox
        aoi_wkt_str = f"POLYGON(({west} {south}, {east} {south}, {east} {north}, {west} {north}, {west} {south}))"
        aoi_crs_str = "EPSG:4326"
        logger.info(f"Using bbox as AOI: {bbox}")
    elif aoi is not None:
        try:
            if isinstance(aoi, str):  # WKT string
                aoi_geom = wkt_loads(aoi)
                aoi_crs_str = "EPSG:4326"
                logger.info(f"Parsed AOI from WKT string, assuming CRS: {aoi_crs_str}")
            elif isinstance(aoi, BaseGeometry):  # Shapely geometry
                aoi_geom = aoi
                aoi_crs_str = "EPSG:4326"
                logger.info("Using Shapely geometry as AOI, assuming EPSG:4326")
            elif isinstance(aoi, (gpd.GeoDataFrame, gpd.GeoSeries)):  # GeoPandas object
                if not aoi.crs:
                    raise ValueError("GeoPandas object must have a defined CRS")
                aoi_crs_str = aoi.crs.to_string()
                aoi_geom = aoi.unary_union
                logger.info(f"Using GeoPandas geometry as AOI with CRS: {aoi_crs_str}")
            else:
                raise TypeError(f"Unsupported AOI type: {type(aoi)}")
            
            aoi_wkt_str = aoi_geom.wkt
        except Exception as e:
            raise ValueError(f"Failed to process AOI: {e}") from e
    else:
        # Use a default AOI that covers the entire world
        aoi_wkt_str = "POLYGON((-180 -90, 180 -90, 180 90, -180 90, -180 -90))"
        aoi_crs_str = "EPSG:4326"
        logger.info("Using default global AOI since none was provided")

    # Build load operation details
    load_operation_details = {
        "collection": collection,
        "bands": bands or [],
        "aoi_wkt": aoi_wkt_str,
        "aoi_crs": aoi_crs_str,
        "datetime": datetime,
        "filters": filters or {},
        "limit": limit or 0,
    }

    # Create Workflow instance
    collection_instance = Workflow(
        operation_type="LOAD",
        operation_details=load_operation_details,
        processing_engine_grpc_target=final_grpc_target,
        processing_engine_flight_target=final_flight_target
    )
    
    logger.info(f"Created lazy Workflow for '{collection}'")
    return collection_instance


def load(
    collection: str,
    bands: Optional[List[str]] = None,
    bbox: Optional[List[float]] = None,
    aoi: Optional[Union[BaseGeometry, gpd.GeoDataFrame, gpd.GeoSeries, str]] = None,
    datetime: Optional[str] = None,
    filters: Optional[Dict[str, Any]] = None,
    limit: Optional[int] = None,
    processing_engine_grpc_target: Optional[str] = None,
    processing_engine_flight_target: Optional[str] = None,
) -> 'Workflow':
    """
    Load geospatial data as a lazy Workflow.
    
    This is the main entry point for starting a data processing workflow with the SDK.
    The returned collection represents a lazy computation graph that will be executed
    when compute() is called.
    
    Args:
        collection: Name or ID of the geospatial data collection
        bands: List of band names to select
        bbox: Bounding box as [west, south, east, north] in EPSG:4326
        aoi: Area of interest as Shapely geometry, WKT string, or GeoPandas object
        datetime: Time range or specific datetime (e.g., "2020-01-01/2020-12-31")
        filters: Additional filters for data selection
        limit: Maximum number of scenes to process
        processing_engine_grpc_target: gRPC endpoint for the processing engine
        processing_engine_flight_target: Arrow Flight endpoint for the processing engine
    
    Returns:
        A lazy Workflow representing the data loading operation
    
    Examples:
        >>> import terrafloww as tfw
        >>> collection = tfw.load("sentinel-2-l2a", bands=["B4", "B8"])
        >>> # Apply NDVI operation
        >>> ndvi_collection = collection.apply("ndvi", {"red_band": "B4", "nir_band": "B8"})
        >>> # Execute computation
        >>> result = ndvi_collection.compute()
        
        >>> # Using bbox for area of interest
        >>> collection = tfw.load(
        ...     "sentinel-2-l2a",
        ...     bbox=[-74.0, 40.6, -73.9, 40.8],  # NYC area
        ...     datetime="2024-06-01/2024-06-30"
        ... )
    """
    return asyncio.run(
        _load_async(
            collection=collection,
            bands=bands,
            bbox=bbox,
            aoi=aoi,
            datetime=datetime,
            filters=filters,
            limit=limit,
            processing_engine_grpc_target=processing_engine_grpc_target,
            processing_engine_flight_target=processing_engine_flight_target,
        )
    )
