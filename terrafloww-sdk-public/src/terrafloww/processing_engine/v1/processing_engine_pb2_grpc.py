# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from terrafloww.processing_engine.v1 import processing_engine_pb2 as terrafloww_dot_processing__engine_dot_v1_dot_processing__engine__pb2

GRPC_GENERATED_VERSION = '1.71.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in terrafloww/processing_engine/v1/processing_engine_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class ProcessingEngineServiceStub(object):
    """Define the main gRPC service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ExecuteWorkflow = channel.unary_unary(
                '/terrafloww.processing_engine.v1.ProcessingEngineService/ExecuteWorkflow',
                request_serializer=terrafloww_dot_processing__engine_dot_v1_dot_processing__engine__pb2.ExecuteWorkflowRequest.SerializeToString,
                response_deserializer=terrafloww_dot_processing__engine_dot_v1_dot_processing__engine__pb2.ExecuteWorkflowResponse.FromString,
                _registered_method=True)


class ProcessingEngineServiceServicer(object):
    """Define the main gRPC service
    """

    def ExecuteWorkflow(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ProcessingEngineServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ExecuteWorkflow': grpc.unary_unary_rpc_method_handler(
                    servicer.ExecuteWorkflow,
                    request_deserializer=terrafloww_dot_processing__engine_dot_v1_dot_processing__engine__pb2.ExecuteWorkflowRequest.FromString,
                    response_serializer=terrafloww_dot_processing__engine_dot_v1_dot_processing__engine__pb2.ExecuteWorkflowResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'terrafloww.processing_engine.v1.ProcessingEngineService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('terrafloww.processing_engine.v1.ProcessingEngineService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ProcessingEngineService(object):
    """Define the main gRPC service
    """

    @staticmethod
    def ExecuteWorkflow(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/terrafloww.processing_engine.v1.ProcessingEngineService/ExecuteWorkflow',
            terrafloww_dot_processing__engine_dot_v1_dot_processing__engine__pb2.ExecuteWorkflowRequest.SerializeToString,
            terrafloww_dot_processing__engine_dot_v1_dot_processing__engine__pb2.ExecuteWorkflowResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
