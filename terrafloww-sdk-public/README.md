# Terrafloww SDK

**Lightweight Python SDK for the Terrafloww Geospatial AI Platform**

[![Python 3.10+](https://img.shields.io/badge/python-3.10+-blue.svg)](https://www.python.org/downloads/)
[![License: Proprietary](https://img.shields.io/badge/License-Terrafloww%20Labs%20Proprietary-red.svg)](LICENSE)

## Overview

The Terrafloww SDK provides a clean, lightweight Python interface for processing satellite and aerial imagery at scale. All heavy processing happens server-side on the Terrafloww Platform.

## Quick Start

### Installation

```bash
pip install terrafloww
```

### Basic Usage

```python
import terrafloww as tfw

# Future: Just set your API key once
# tfw.configure(api_key="your_api_key_here")

# Load satellite imagery
collection = tfw.load(
    "sentinel-2-l2a",
    bands=["red", "nir"],
    bbox=[-74.0, 40.6, -73.9, 40.8],  # NYC area
    datetime="2024-06-01/2024-06-30"
)

# Apply NDVI calculation
ndvi = collection.apply("terrafloww.spectral.ndvi", {
    "red_band": "red",
    "nir_band": "nir"
})

# Execute and get results
result = ndvi.head(10).compute()
print(f"Processed {result.num_rows} spatial windows")
```

## Key Features

- **🚀 Lightweight**: No heavy dependencies 
- **☁️ Cloud-Native**: All processing happens server-side
- **🔗 Simple API**: Familiar pandas-like interface
- **📊 Arrow Integration**: Efficient data transfer with PyArrow
- **🌍 Geospatial Ready**: Built-in support for common geospatial operations

## API Reference

### Loading Data

```python
# Load by collection name
collection = tfw.load("sentinel-2-l2a")

# Filter by area of interest
collection = tfw.load(
    "sentinel-2-l2a",
    bbox=[-74.0, 40.6, -73.9, 40.8],  # [west, south, east, north]
    datetime="2024-01-01/2024-12-31"
)

# Select specific bands
collection = tfw.load(
    "sentinel-2-l2a", 
    bands=["B2", "B3", "B4", "B8"]
)
```

### Applying Operations

```python
# Built-in operations
ndvi = collection.apply("terrafloww.spectral.ndvi", {"red_band": "red", "nir_band": "nir"})
ndwi = collection.apply("terrafloww.spectral.ndwi", {"green_band": "green", "nir_band": "nir"})

# Chain operations
result = (collection
    .apply("terrafloww.spectral.ndvi", {"red_band": "red", "nir_band": "nir"})
    .apply("terrafloww.filters.threshold", {"column": "ndvi", "min_value": 0.3})
    .head(100)
    .compute())
```

### Execution

```python
# Limit results
limited = collection.head(50)  # First 50 spatial windows

# Execute computation
result = collection.compute()  # Returns PyArrow Table

# Get results as pandas DataFrame
df = result.to_pandas()
```

## Configuration

### Authentication (Coming Soon)

```python
import terrafloww as tfw

# Future: Simple API key authentication
tfw.configure(api_key="tfw_live_abc123...")

# Or environment variable
# export TERRAFLOWW_API_KEY="tfw_live_abc123..."
```

### Current Development Setup

```bash
# For local development
export TFW_PROCESSING_GRPC_TARGET="localhost:50051"
export TFW_PROCESSING_FLIGHT_TARGET="grpc+tcp://localhost:50052"

# For Kubernetes deployment (via port-forward)
kubectl port-forward -n terrafloww-platform svc/terrafloww-processing-engine-svc 50051:50051 50052:50052
# Then use same localhost endpoints above
```

**✅ Tested Deployments**: Local Ray cluster, Kubernetes platform, hybrid setups
**🎯 Production Ready**: Works with real workloads and data processing

## Available Operations

| Operation | Description | Parameters |
|-----------|-------------|------------|
| `terrafloww.spectral.ndvi` | Normalized Difference Vegetation Index | `red_band`, `nir_band` |
| `terrafloww.spectral.ndwi` | Normalized Difference Water Index | `green_band`, `nir_band` |
| `terrafloww.spectral.evi` | Enhanced Vegetation Index | `red_band`, `nir_band`, `blue_band` |
| `terrafloww.filters.threshold` | Apply threshold filter | `column`, `min_value`, `max_value` |
| `terrafloww.filters.mask` | Apply mask based on condition | `column`, `condition` |

## Examples

### Vegetation Analysis

```python
import terrafloww as tfw

# Future: Simple setup
# tfw.configure(api_key="your_api_key")

# Load Sentinel-2 data for agricultural area
collection = tfw.load(
    "sentinel-2-l2a",
    bands=["red", "nir", "green", "blue"],
    bbox=[-120.5, 35.0, -120.0, 35.5],  # Central Valley, CA
    datetime="2024-06-01/2024-08-31"
)

# Calculate vegetation indices
ndvi = collection.apply("terrafloww.spectral.ndvi", {"red_band": "red", "nir_band": "nir"})
healthy_vegetation = ndvi.apply("terrafloww.filters.threshold", {"column": "ndvi", "min_value": 0.4})

# Get results
result = healthy_vegetation.head(200).compute()
print(f"Found {result.num_rows} healthy vegetation areas")
```

### Water Detection

```python
# Load data for water body analysis
collection = tfw.load(
    "sentinel-2-l2a",
    bands=["green", "nir"],
    bbox=[-74.2, 40.4, -73.7, 40.9],  # NYC water bodies
    datetime="2024-07-01/2024-07-31"
)

# Calculate water index
ndwi = collection.apply("terrafloww.spectral.ndwi", {"green_band": "green", "nir_band": "nir"})
water_areas = ndwi.apply("terrafloww.filters.threshold", {"column": "ndwi", "min_value": 0.2})

result = water_areas.compute()
```

## Requirements

- Python 3.10+
- Internet connection (for API calls to Terrafloww Platform)

## Development Status

**Current Version (v0.3.0)**:
- ✅ **Production Ready**: Tested with both local and Kubernetes deployments
- ✅ **Full Functionality**: Load, apply, compute operations work end-to-end
- ✅ **Platform Agnostic**: Works with any Terrafloww Platform deployment
- ⚠️ **Development Setup**: Requires endpoint configuration (temporary)

**Coming Soon (v1.0.0)**:
- 🔐 **API Key Auth**: Simple `tfw.configure(api_key="...")` setup
- 🚀 **Zero Config**: Auto-discovery of platform endpoints
- 🎯 **Cleaner API**: Simplified function names and interface

## Support

- **Documentation**: [docs.terrafloww.com](https://docs.terrafloww.com)
- **Issues**: [GitHub Issues](https://github.com/terrafloww/terrafloww-sdk/issues)
- **Contact**: <EMAIL>

## License

Terrafloww Labs Proprietary License. See [LICENSE](LICENSE) for details.
