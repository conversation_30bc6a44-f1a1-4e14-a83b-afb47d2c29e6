# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""
Test basic data access for Bangalore AOI 2024 data.

This test verifies that the ingested Bangalore data can be loaded
and accessed through the SDK.
"""

import os
import sys
from shapely.geometry import Polygon

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

import terrafloww as tfw


def test_bangalore_data_load():
    """Test that Bangalore 2024 data can be loaded via SDK."""
    
    # Define Bangalore AOI
    aoi_polygon = Polygon([
        (77.55, 13.01), (77.58, 13.01), 
        (77.58, 13.08), (77.55, 13.08), 
        (77.55, 13.01)
    ])
    
    print("🎯 Testing Bangalore AOI data access...")
    print(f"AOI: {aoi_polygon.bounds}")
    
    try:
        # Load data for a small date range first
        workflow = tfw.load(
            "sentinel-2-l2a",
            bbox=list(aoi_polygon.bounds),  # [minx, miny, maxx, maxy]
            datetime="2024-01-01/2024-01-31",  # January 2024
            bands=["red", "nir"]
        )
        
        print(f"✅ Workflow created: {type(workflow).__name__}")
        print(f"📊 Workflow type: {workflow._operation_type}")
        
        # Test that we can apply NDVI
        ndvi_workflow = workflow.apply("ndvi", {
            "red_band": "red",
            "nir_band": "nir"
        })
        
        print(f"✅ NDVI workflow created: {type(ndvi_workflow).__name__}")
        
        # Test head operation
        limited_workflow = ndvi_workflow.head(2)
        print(f"✅ Head workflow created with limit: 2")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading Bangalore data: {e}")
        return False


def test_bangalore_data_compute():
    """Test that Bangalore data can be computed (if backend is available)."""
    
    # Define Bangalore AOI
    aoi_polygon = Polygon([
        (77.55, 13.01), (77.58, 13.01), 
        (77.58, 13.08), (77.55, 13.08), 
        (77.55, 13.01)
    ])
    
    print("🧮 Testing Bangalore data computation...")
    
    try:
        # Load and process data
        workflow = tfw.load(
            "sentinel-2-l2a",
            bbox=list(aoi_polygon.bounds),
            datetime="2024-01-01/2024-01-15",  # Small date range
            bands=["red", "nir"]
        ).apply("ndvi", {
            "red_band": "red", 
            "nir_band": "nir"
        }).head(1)  # Just 1 scene for testing
        
        print("🚀 Attempting to compute results...")
        
        # This will only work if backend services are running
        results = workflow.compute()
        
        print(f"✅ Computation successful!")
        print(f"📊 Results type: {type(results)}")
        print(f"📏 Results shape: {results.shape if hasattr(results, 'shape') else 'N/A'}")
        
        if hasattr(results, 'column_names'):
            print(f"📋 Columns: {results.column_names}")
            
        return True
        
    except Exception as e:
        print(f"⚠️ Computation failed (expected if backend not running): {e}")
        # This is expected if backend services aren't running
        return False


if __name__ == "__main__":
    print("🧪 Running Bangalore Data Access Tests")
    print("=" * 50)
    
    # Test 1: Basic data loading
    print("\n📋 Test 1: Basic Data Loading")
    load_success = test_bangalore_data_load()
    
    # Test 2: Data computation (optional)
    print("\n📋 Test 2: Data Computation")
    compute_success = test_bangalore_data_compute()
    
    print("\n" + "=" * 50)
    print("🎯 Test Summary:")
    print(f"  📊 Data Loading: {'✅ PASS' if load_success else '❌ FAIL'}")
    print(f"  🧮 Data Computation: {'✅ PASS' if compute_success else '⚠️ SKIP (backend not available)'}")
    
    if load_success:
        print("\n🎉 Bangalore data is accessible via SDK!")
        print("Ready to proceed with NDVI time series test development.")
    else:
        print("\n❌ Bangalore data access failed.")
        print("Check data ingestion and SDK configuration.")
