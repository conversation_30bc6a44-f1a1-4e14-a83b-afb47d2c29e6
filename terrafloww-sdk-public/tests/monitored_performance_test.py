# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""
Monitored Performance Test for Deep Analysis

This test runs the NDVI time series analysis while providing detailed
timing and monitoring information for performance investigation.
"""

import os
import sys
import time
import json
from datetime import datetime
from typing import Dict, Any
import numpy as np
import pandas as pd
import pyarrow as pa
from shapely.geometry import Polygon

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

import terrafloww as tfw


class PerformanceMonitor:
    """Monitor and collect detailed performance metrics."""
    
    def __init__(self):
        self.metrics = {
            'test_start_time': None,
            'test_end_time': None,
            'phases': {},
            'system_info': {},
            'detailed_timings': []
        }
    
    def start_test(self):
        """Start the performance test."""
        self.metrics['test_start_time'] = time.time()
        self.metrics['test_start_datetime'] = datetime.now().isoformat()
        print(f"🚀 Performance test started at {self.metrics['test_start_datetime']}")
    
    def end_test(self):
        """End the performance test."""
        self.metrics['test_end_time'] = time.time()
        self.metrics['test_end_datetime'] = datetime.now().isoformat()
        self.metrics['total_duration'] = self.metrics['test_end_time'] - self.metrics['test_start_time']
        print(f"🏁 Performance test completed at {self.metrics['test_end_datetime']}")
        print(f"⏱️ Total test duration: {self.metrics['total_duration']:.2f} seconds")
    
    def start_phase(self, phase_name: str):
        """Start timing a phase."""
        if phase_name not in self.metrics['phases']:
            self.metrics['phases'][phase_name] = {}
        
        self.metrics['phases'][phase_name]['start_time'] = time.time()
        self.metrics['phases'][phase_name]['start_datetime'] = datetime.now().isoformat()
        print(f"📋 Starting phase: {phase_name}")
    
    def end_phase(self, phase_name: str, additional_info: Dict[str, Any] = None):
        """End timing a phase."""
        if phase_name not in self.metrics['phases']:
            print(f"❌ Phase {phase_name} was not started")
            return
        
        end_time = time.time()
        start_time = self.metrics['phases'][phase_name]['start_time']
        duration = end_time - start_time
        
        self.metrics['phases'][phase_name]['end_time'] = end_time
        self.metrics['phases'][phase_name]['end_datetime'] = datetime.now().isoformat()
        self.metrics['phases'][phase_name]['duration'] = duration
        
        if additional_info:
            self.metrics['phases'][phase_name]['info'] = additional_info
        
        print(f"✅ Completed phase: {phase_name} ({duration:.2f}s)")
    
    def add_timing(self, operation: str, duration: float, details: Dict[str, Any] = None):
        """Add a detailed timing measurement."""
        timing_entry = {
            'timestamp': time.time(),
            'datetime': datetime.now().isoformat(),
            'operation': operation,
            'duration': duration,
            'details': details or {}
        }
        self.metrics['detailed_timings'].append(timing_entry)
    
    def save_metrics(self, filename: str = None):
        """Save metrics to JSON file."""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"performance_metrics_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(self.metrics, f, indent=2, default=str)
        
        print(f"📊 Performance metrics saved to: {filename}")
        return filename


def run_monitored_ndvi_test():
    """Run NDVI test with detailed performance monitoring."""
    
    monitor = PerformanceMonitor()
    monitor.start_test()
    
    try:
        # Phase 1: Setup and Configuration
        monitor.start_phase("setup")
        
        # Define Bangalore AOI
        aoi_polygon = Polygon([
            (77.55, 13.01), (77.58, 13.01), 
            (77.58, 13.08), (77.55, 13.08), 
            (77.55, 13.01)
        ])
        
        print(f"📍 AOI bounds: {aoi_polygon.bounds}")
        print(f"📅 Date range: 2024-01-01 to 2024-12-31")
        
        monitor.end_phase("setup", {
            'aoi_bounds': list(aoi_polygon.bounds),
            'date_range': '2024-01-01/2024-12-31'
        })
        
        # Phase 2: Workflow Creation
        monitor.start_phase("workflow_creation")
        
        workflow_start = time.time()
        workflow = tfw.load(
            "sentinel-2-l2a",
            bbox=list(aoi_polygon.bounds),
            datetime="2024-01-01/2024-12-31",  # Full year
            bands=["red", "nir"]
        ).apply("terrafloww.spectral.ndvi", {
            "red_band": "red",
            "nir_band": "nir"
        })
        workflow_creation_time = time.time() - workflow_start
        
        monitor.add_timing("workflow_creation", workflow_creation_time)
        monitor.end_phase("workflow_creation", {
            'workflow_type': type(workflow).__name__,
            'creation_time': workflow_creation_time
        })
        
        # Phase 3: Backend Processing (Job Submission to Data Receipt)
        monitor.start_phase("backend_processing")
        
        print("🚀 Submitting job to backend...")
        backend_start = time.time()
        
        # This includes: job submission, planning, Ray execution, Flight transfer
        results = workflow.compute()
        
        backend_duration = time.time() - backend_start
        monitor.add_timing("backend_total", backend_duration, {
            'scenes_processed': len(results),
            'result_columns': results.column_names,
            'result_shape': (len(results), len(results.column_names))
        })
        
        monitor.end_phase("backend_processing", {
            'total_scenes': len(results),
            'processing_rate': len(results) / backend_duration,
            'columns': results.column_names
        })
        
        # Phase 4: Data Processing
        monitor.start_phase("data_processing")
        
        processing_start = time.time()
        
        # Convert to pandas
        df_start = time.time()
        df = results.to_pandas()
        df_conversion_time = time.time() - df_start
        monitor.add_timing("pandas_conversion", df_conversion_time)
        
        # Extract date
        date_extraction_start = time.time()
        df['date'] = pd.to_datetime(df['datetime']).dt.date
        date_extraction_time = time.time() - date_extraction_start
        monitor.add_timing("date_extraction", date_extraction_time)
        
        # NDVI processing
        ndvi_processing_start = time.time()
        
        def extract_valid_ndvi_mean(ndvi_array):
            if not hasattr(ndvi_array, '__len__'):
                return float(ndvi_array)
            
            valid_mask = (ndvi_array >= -1) & (ndvi_array <= 1)
            valid_ndvi = ndvi_array[valid_mask]
            
            if len(valid_ndvi) > 0:
                return float(valid_ndvi.mean())
            else:
                return np.nan
        
        df['ndvi_scalar'] = df['ndvi'].apply(extract_valid_ndvi_mean)
        ndvi_processing_time = time.time() - ndvi_processing_start
        monitor.add_timing("ndvi_processing", ndvi_processing_time)
        
        # Temporal aggregation
        aggregation_start = time.time()
        daily_stats = df.groupby('date').agg({
            'ndvi_scalar': ['mean', 'std', 'count'],
            'chunk_id': 'count'
        }).round(4)
        daily_stats.columns = ['ndvi_mean', 'ndvi_std', 'ndvi_count', 'spatial_windows']
        daily_stats = daily_stats.reset_index()
        daily_stats['datetime'] = pd.to_datetime(daily_stats['date'])
        aggregation_time = time.time() - aggregation_start
        monitor.add_timing("temporal_aggregation", aggregation_time)
        
        total_processing_time = time.time() - processing_start
        
        monitor.end_phase("data_processing", {
            'unique_dates': len(daily_stats),
            'ndvi_range': [float(daily_stats['ndvi_mean'].min()), float(daily_stats['ndvi_mean'].max())],
            'processing_breakdown': {
                'pandas_conversion': df_conversion_time,
                'date_extraction': date_extraction_time,
                'ndvi_processing': ndvi_processing_time,
                'temporal_aggregation': aggregation_time
            }
        })
        
        # Phase 5: Results Analysis
        monitor.start_phase("results_analysis")
        
        # Calculate statistics
        total_pixels = df['ndvi'].apply(lambda x: len(x) if hasattr(x, '__len__') else 1).sum()
        valid_pixels = df['ndvi'].apply(lambda x: len(x[(x >= -1) & (x <= 1)]) if hasattr(x, '__len__') else (1 if -1 <= x <= 1 else 0)).sum()
        
        monitor.end_phase("results_analysis", {
            'total_pixels': int(total_pixels),
            'valid_pixels': int(valid_pixels),
            'pixel_validity_rate': float(valid_pixels / total_pixels),
            'seasonal_analysis': {
                'spring': float(daily_stats[daily_stats['datetime'].dt.month.isin([3,4,5])]['ndvi_mean'].mean()),
                'summer': float(daily_stats[daily_stats['datetime'].dt.month.isin([6,7,8])]['ndvi_mean'].mean()),
                'autumn': float(daily_stats[daily_stats['datetime'].dt.month.isin([9,10,11])]['ndvi_mean'].mean()),
                'winter': float(daily_stats[daily_stats['datetime'].dt.month.isin([12,1,2])]['ndvi_mean'].mean())
            }
        })
        
        print(f"✅ Test completed successfully!")
        print(f"📊 Processed {len(results)} scenes")
        print(f"📈 Generated {len(daily_stats)} daily NDVI measurements")
        print(f"🔍 Pixel validity: {valid_pixels/total_pixels*100:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        monitor.end_test()
        metrics_file = monitor.save_metrics()
        
        # Print performance summary
        print("\n" + "="*60)
        print("📊 PERFORMANCE SUMMARY")
        print("="*60)
        
        for phase_name, phase_data in monitor.metrics['phases'].items():
            if 'duration' in phase_data:
                duration = phase_data['duration']
                percentage = (duration / monitor.metrics['total_duration']) * 100
                print(f"{phase_name:>20}: {duration:>8.2f}s ({percentage:>5.1f}%)")
        
        print(f"{'TOTAL':>20}: {monitor.metrics['total_duration']:>8.2f}s")
        print("="*60)
        
        return metrics_file


if __name__ == "__main__":
    print("🔍 Starting Monitored Performance Test")
    print("=" * 60)
    
    metrics_file = run_monitored_ndvi_test()
    
    if metrics_file:
        print(f"\n📊 Detailed metrics saved to: {metrics_file}")
        print("🔍 Use this data for performance analysis and optimization")
    
    print("\n🎯 Performance test complete!")
