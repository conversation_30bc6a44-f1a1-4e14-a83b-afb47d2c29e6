#!/usr/bin/env python3
"""
Test the public SDK against the real Terrafloww Platform.
This replicates the test_ndvi.py test using the lightweight public SDK.
"""

import sys
import os

# Add the public SDK to path
sys.path.insert(0, '/home/<USER>/Work/platform-build/terrafloww-sdk-public/src')

def test_public_sdk_ndvi():
    import re
    import terrafloww as tfw
    from shapely.geometry import box

    # Get endpoints from environment variables or use localhost defaults
    grpc_target = os.getenv("PROCESSING_ENGINE_GRPC_TARGET", "localhost:50051")
    flight_target = os.getenv("PROCESSING_ENGINE_FLIGHT_TARGET", "grpc+tcp://localhost:50052")

    print(f"🧪 Testing Public SDK with real platform")
    print(f"Using gRPC target: {grpc_target}")
    print(f"Using Flight target: {flight_target}")

    # Create AOI for California region (San Francisco Bay Area)
    california_aoi = box(-122.5, 37.7, -122.3, 37.9)

    # Load data using public SDK
    collection = tfw.load(
        "sentinel-2-l2a",
        bands=["red", "nir"],  # Using common names that match the actual data
        aoi=california_aoi,  # California region (San Francisco Bay Area)
        datetime="2024-06-01/2024-06-23",  # June 2024 timeframe
        processing_engine_grpc_target=grpc_target,
        processing_engine_flight_target=flight_target
    )

    print("✅ Load operation created successfully")

    # Apply NDVI operation using public SDK
    # Use the full function ID and correct parameter names (matching internal implementation)
    ndvi_collection = collection.apply("terrafloww.spectral.ndvi", {"red_band": "red", "nir_band": "nir"})
    
    print("✅ NDVI operation added successfully")

    # Apply head limit to get exactly 2 unique spatial windows
    limited_collection = ndvi_collection.head(2)
    
    print("✅ Head limit applied successfully")

    # Execute computation
    print("🚀 Executing workflow on platform...")
    result = limited_collection.compute()

    # Verify results
    print(f"Result table shape: {result.num_rows} rows, {len(result.column_names)} columns")
    print(f"Columns: {result.column_names}")

    # Debug: Check the actual data structure
    df = result.to_pandas()
    print(f"DataFrame columns: {list(df.columns)}")
    if 'bands' in df.columns:
        print(f"Bands in first row: {df['bands'].iloc[0] if len(df) > 0 else 'No data'}")

    # Since we requested head(2), we should get exactly 2 unique spatial windows
    assert result.num_rows == 2, f"Expected exactly 2 chunks, got {result.num_rows}"
    print("✅ result num rows is as expected")

    # Check if NDVI is in the bands or as a separate column
    has_ndvi = "ndvi" in result.column_names
    if not has_ndvi and 'bands' in df.columns:
        # Check if NDVI is in the bands array
        first_bands = df['bands'].iloc[0] if len(df) > 0 else []
        has_ndvi = 'ndvi' in first_bands if first_bands else False
        print(f"NDVI found in bands array: {has_ndvi}")

    assert has_ndvi, "NDVI should be present either as column or in bands"
    print("✅ ndvi present in result")

    # Verify unique spatial windows
    unique_windows = set()
    for row in result.to_pandas().itertuples():
        # chunk_id is now: {scene_id}_T{tile_r:02d}{tile_c:02d}_{suffix}
        m = re.match(r'(.+)_T(\d{2})(\d{2})_', row.chunk_id)
        assert m, f"Unexpected chunk_id format: {row.chunk_id}"
        scene_id = m.group(1)
        tile_r, tile_c = int(m.group(2)), int(m.group(3))
        unique_windows.add((scene_id, tile_r, tile_c))

    assert len(unique_windows) == 2, (
        f"Expected exactly 2 unique windows, got {len(unique_windows)}"
    )
    print("✅ unique windows are as expected")
    
    print("🎉 PUBLIC SDK TEST PASSED! All functionality working correctly.")
    return True

if __name__ == "__main__":
    try:
        test_public_sdk_ndvi()
        print("\n✅ SUCCESS: Public SDK can execute real workloads on the platform!")
    except Exception as e:
        print(f"\n❌ FAILED: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
