# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Basic tests for the Terrafloww SDK.
"""

import pytest
import terrafloww as tfw


def test_import():
    """Test that the SDK can be imported."""
    assert hasattr(tfw, 'load')
    assert hasattr(tfw, 'Workflow')


def test_load_creates_workflow():
    """Test that load() creates a Workflow."""
    workflow = tfw.load("test-collection")
    assert isinstance(workflow, tfw.Workflow)


def test_workflow_chaining():
    """Test that operations can be chained."""
    workflow = tfw.load("test-collection")
    result = workflow.apply("ndvi", {"red_band": "B4", "nir_band": "B8"}).head(5)
    assert isinstance(result, tfw.Workflow)


def test_bbox_parameter():
    """Test that bbox parameter works."""
    workflow = tfw.load(
        "sentinel-2-l2a",
        bbox=[-74.0, 40.6, -73.9, 40.8],
        datetime="2024-06-01/2024-06-30"
    )
    assert isinstance(workflow, tfw.Workflow)


# Note: These tests don't actually execute compute() since that requires
# a running Terrafloww Platform. For integration tests, see the main
# internal-platform-v2/sdk/src/tests/ directory.
