# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""
Debug NDVI values to understand what's happening with the data.

This test examines the raw data to understand why NDVI values are outside
the expected range of -1 to 1.
"""

import os
import sys
import numpy as np
import pandas as pd
from shapely.geometry import Polygon

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

import terrafloww as tfw


def debug_ndvi_calculation():
    """Debug the NDVI calculation and data values."""
    
    print("🔍 Debugging NDVI Values")
    print("=" * 50)
    
    # Define Bangalore AOI
    aoi_polygon = Polygon([
        (77.55, 13.01), (77.58, 13.01), 
        (77.58, 13.08), (77.55, 13.08), 
        (77.55, 13.01)
    ])
    
    print(f"📍 AOI bounds: {aoi_polygon.bounds}")
    
    # Load just one scene for detailed analysis
    print("\n🔍 Loading single scene for detailed analysis...")
    workflow = tfw.load(
        "sentinel-2-l2a",
        bbox=list(aoi_polygon.bounds),
        datetime="2024-01-03/2024-01-04",  # Single day
        bands=["red", "nir"]
    ).apply("terrafloww.spectral.ndvi", {
        "red_band": "red",
        "nir_band": "nir"
    }).head(1)  # Just one spatial window
    
    print("🚀 Computing single scene...")
    results = workflow.compute()
    
    if len(results) == 0:
        print("❌ No results returned")
        return
    
    print(f"✅ Got {len(results)} result(s)")
    
    # Convert to pandas for analysis
    df = results.to_pandas()
    
    print(f"\n📋 Available columns: {df.columns.tolist()}")
    
    # Examine the first row in detail
    row = df.iloc[0]
    
    print(f"\n🔍 Examining first row:")
    print(f"  📅 Datetime: {row['datetime']}")
    print(f"  🆔 Chunk ID: {row['chunk_id']}")
    print(f"  📐 Shape: {row['shape']}")
    print(f"  🗺️ Bounds: {row['bounds']}")
    
    # Examine bands data
    print(f"\n🎵 Bands column:")
    bands_data = row['bands']
    print(f"  Type: {type(bands_data)}")
    print(f"  Content: {bands_data}")
    
    # Examine raster_data
    print(f"\n🖼️ Raster data:")
    raster_data = row['raster_data']
    print(f"  Type: {type(raster_data)}")
    print(f"  Shape: {raster_data.shape if hasattr(raster_data, 'shape') else 'No shape'}")
    
    if hasattr(raster_data, 'shape') and len(raster_data.shape) >= 2:
        print(f"  Dimensions: {raster_data.shape}")
        print(f"  Data type: {raster_data.dtype}")
        print(f"  Min value: {raster_data.min()}")
        print(f"  Max value: {raster_data.max()}")
        print(f"  Mean value: {raster_data.mean()}")
        
        # If it's 3D, examine each band
        if len(raster_data.shape) == 3:
            print(f"\n📊 Band-by-band analysis:")
            for i, band_name in enumerate(['red', 'nir', 'ndvi']):
                if i < raster_data.shape[0]:
                    band_data = raster_data[i]
                    print(f"  {band_name:>4}: min={band_data.min():>8.2f}, max={band_data.max():>8.2f}, mean={band_data.mean():>8.2f}")
    
    # Examine NDVI column
    print(f"\n🌱 NDVI column:")
    ndvi_data = row['ndvi']
    print(f"  Type: {type(ndvi_data)}")
    print(f"  Shape: {ndvi_data.shape if hasattr(ndvi_data, 'shape') else 'No shape'}")
    
    if hasattr(ndvi_data, 'shape'):
        print(f"  Data type: {ndvi_data.dtype}")
        print(f"  Min value: {ndvi_data.min()}")
        print(f"  Max value: {ndvi_data.max()}")
        print(f"  Mean value: {ndvi_data.mean()}")
        
        # Show some sample values
        flat_ndvi = ndvi_data.flatten()
        print(f"  Sample values: {flat_ndvi[:10]}")
        
        # Check for valid NDVI range
        valid_ndvi = flat_ndvi[(flat_ndvi >= -1) & (flat_ndvi <= 1)]
        print(f"  Values in valid range [-1,1]: {len(valid_ndvi)}/{len(flat_ndvi)} ({len(valid_ndvi)/len(flat_ndvi)*100:.1f}%)")
        
        if len(valid_ndvi) > 0:
            print(f"  Valid NDVI range: {valid_ndvi.min():.3f} to {valid_ndvi.max():.3f}")
            print(f"  Valid NDVI mean: {valid_ndvi.mean():.3f}")


def debug_manual_ndvi_calculation():
    """Manually calculate NDVI to verify the calculation."""
    
    print("\n" + "=" * 50)
    print("🧮 Manual NDVI Calculation")
    print("=" * 50)
    
    # Define Bangalore AOI
    aoi_polygon = Polygon([
        (77.55, 13.01), (77.58, 13.01), 
        (77.58, 13.08), (77.55, 13.08), 
        (77.55, 13.01)
    ])
    
    # Load raw bands without NDVI calculation
    print("🔍 Loading raw red and NIR bands...")
    workflow = tfw.load(
        "sentinel-2-l2a",
        bbox=list(aoi_polygon.bounds),
        datetime="2024-01-03/2024-01-04",  # Single day
        bands=["red", "nir"]
    ).head(1)  # Just one spatial window
    
    print("🚀 Computing raw bands...")
    results = workflow.compute()
    
    if len(results) == 0:
        print("❌ No results returned")
        return
    
    # Convert to pandas for analysis
    df = results.to_pandas()
    row = df.iloc[0]
    
    print(f"✅ Got raw data")
    print(f"📋 Columns: {df.columns.tolist()}")
    
    # Examine raster_data for raw bands
    raster_data = row['raster_data']
    print(f"\n🖼️ Raw raster data:")
    print(f"  Shape: {raster_data.shape}")
    print(f"  Data type: {raster_data.dtype}")
    
    if len(raster_data.shape) == 3 and raster_data.shape[0] >= 2:
        red_band = raster_data[0]  # Assuming red is first
        nir_band = raster_data[1]  # Assuming NIR is second
        
        print(f"\n📊 Band analysis:")
        print(f"  Red band: min={red_band.min():>8.2f}, max={red_band.max():>8.2f}, mean={red_band.mean():>8.2f}")
        print(f"  NIR band: min={nir_band.min():>8.2f}, max={nir_band.max():>8.2f}, mean={nir_band.mean():>8.2f}")
        
        # Manual NDVI calculation: (NIR - Red) / (NIR + Red)
        print(f"\n🧮 Manual NDVI calculation:")
        
        # Avoid division by zero
        denominator = nir_band + red_band
        valid_mask = denominator != 0
        
        manual_ndvi = np.zeros_like(red_band, dtype=np.float32)
        manual_ndvi[valid_mask] = (nir_band[valid_mask] - red_band[valid_mask]) / denominator[valid_mask]
        
        print(f"  Manual NDVI: min={manual_ndvi.min():>8.3f}, max={manual_ndvi.max():>8.3f}, mean={manual_ndvi.mean():>8.3f}")
        
        # Check if values are in expected range
        valid_ndvi = manual_ndvi[(manual_ndvi >= -1) & (manual_ndvi <= 1)]
        print(f"  Values in valid range [-1,1]: {len(valid_ndvi)}/{manual_ndvi.size} ({len(valid_ndvi)/manual_ndvi.size*100:.1f}%)")
        
        if len(valid_ndvi) > 0:
            print(f"  Valid NDVI range: {valid_ndvi.min():.3f} to {valid_ndvi.max():.3f}")
            print(f"  Valid NDVI mean: {valid_ndvi.mean():.3f}")
        
        # Sample some pixel values for detailed inspection
        print(f"\n🔍 Sample pixel analysis (first 5x5 pixels):")
        for i in range(min(5, red_band.shape[0])):
            for j in range(min(5, red_band.shape[1])):
                red_val = red_band[i, j]
                nir_val = nir_band[i, j]
                ndvi_val = manual_ndvi[i, j]
                print(f"  Pixel ({i},{j}): Red={red_val:>8.2f}, NIR={nir_val:>8.2f}, NDVI={ndvi_val:>6.3f}")


if __name__ == "__main__":
    debug_ndvi_calculation()
    debug_manual_ndvi_calculation()
