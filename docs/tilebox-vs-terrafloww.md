// SPDX-FileCopyrightText: Terrafloww Labs, 2025

# Tilebox vs Terrafloww — Concepts, SDKs, and Platform Architecture

Status: Draft (v0.1)
Last updated: 2025-08-22
Author: Augment Agent (Augment Code)

## Executive Summary

- Tilebox and Terrafloww share a separation-of-concerns philosophy: thin SDKs, strong backend services, and efficient columnar transports.
- Tilebox focuses on two product modules:
  - Datasets (Time-series and spatiotemporal) — query, ingest, delete; returns xarray
  - Workflows — task orchestration with task runners, clusters, job graphs
- Terrafloww focuses on:
  - Processing Engine (Ray-based) — plans WindowSpecs and executes distributed kernels; streams via Arrow Flight
  - SDK with lazy workflow builder (load → apply → head → compute) returning Arrow tables
- Key deltas
  - SDK surface: Tilebox datasets API is message/xarray centric and strongly typed via protobuf message pools; Terrafloww is function/DAG centric with server-side operators and Arrow-first results
  - Orchestration: Tilebox exposes a general workflow orchestrator with Task Runners; Terrafloww’s current execution is centered on a Ray-based Processing Engine service
  - Data plane: Both use gRPC; Tilebox converts protobuf messages to xarray; Terrafloww returns Arrow tables via Flight

## Sources Reviewed

- Local site-packages (Tilebox): tilebox.datasets (sync/aio), protobuf conversion, data model, gRPC stubs
- Public docs (Tilebox): Introduction; Workflows → Concepts → Task Runners; Datasets overview
- Terrafloww repository structure and docs: README, docs/KT_doc.md, docs/repo-structure.md; services/processing_engine; libs/tfw_engine_core

## Conceptual Model

### Tilebox

- Modules
  - Datasets:
    - Dataset → Collections → Datapoints (Any/RepeatedAny of message bytes + type_url)
    - Querying by time interval, optional spatial filter (shapely) → paginated gRPC pages → xarray Dataset
    - Ingestion/deletion APIs; collection metadata with availability and counts
  - Workflows:
    - Task orchestration (Tasks, Jobs, Task Runners, Clusters)
    - Runners register Task types, poll for tasks, execute, heartbeat; supports distributed and scaled execution
- SDKs
  - Python sync and async clients (tilebox.datasets.sync / .aio)
  - Go SDK also documented for workflows
- Typing/runtime
  - Message pools for dataset-specific protobuf types; conversion layer message → xarray with field-type inference

### Terrafloww

- Modules
  - Processing Engine:
    - gRPC ExecuteWorkflow receives a logical plan (load/apply/head) and schedules Ray tasks
    - Planner produces WindowSpecs; Driver orchestrates; Workers fetch COGs, run kernels; Flight serves Arrow
  - Metadata/Marketplace/UI services (future/adjacent)
- SDK
  - Minimal Python client that builds a DAG with high-level operations and executes via gRPC + Arrow Flight
- Runtime philosophy
  - Chunky tasks, Hilbert curve sorting for spatial locality, HTTP actor pool for I/O parallelism, GPU-direct streaming in future phases

## SDK Surface: Side-by-side

### Primary abstraction

- Tilebox: dataset- and collection-centric data access
  - Client → DatasetClient → CollectionClient → query/find/ingest/delete
  - Returns xarray.Dataset
- Terrafloww: workflow- and operator-centric pipelines
  - Workflow nodes: LOAD → APPLY_FUNC → HEAD_LIMIT → compute()
  - Returns Arrow table (pyarrow.Table)

### Query and filtering

- Tilebox:
  - Temporal extent (TimeInterval or scalar/tuple/array) required
  - Optional SpatialFilter (intersects/contains, cartesian/spherical)
  - Pagination handled internally with producer/consumer concurrency for decode while fetching next page
- Terrafloww:
  - LOAD encapsulates collection, bands, WKT AOI, CRS, datetime, property filters, scene limits
  - APPLY_FUNC encapsulates function_id + parameters (e.g., ndvi)
  - HEAD_LIMIT constrains number of spatial windows

### Asynchrony

- Tilebox: explicit sync and async clients mirror the API
- Terrafloww: SDK composes lazily and executes async under the hood (compute() awaits gRPC + Flight)

### Data model and output

- Tilebox: Protobuf Any (type_url, bytes) and RepeatedAny streamed over gRPC; converted to xarray with inferred dims, enum handling, repeated fields → coordinate naming
- Terrafloww: Arrow-native streaming over Flight; kernels produce Arrow RecordBatches assembled server-side into a table

## Detailed Code Patterns: Tilebox Datasets SDK

### Promise-based Service Composition
The service layer uses Promise objects to enable sync/async unification:

```python
# tilebox/datasets/service.py:49-53
def list_datasets(self) -> Promise[ListDatasetsResponse]:
    return Promise.resolve(
        self._dataset_service.ListDatasets(ListDatasetsRequest(client_info=_client_info()))
    ).then(ListDatasetsResponse.from_message)
```

Sync clients consume these via `.get()` calls:
```python
# tilebox/datasets/sync/client.py:35-36
def datasets(self) -> Group:
    return self._client.datasets(DatasetClient).get()
```

### gRPC Error Translation
All gRPC stubs are wrapped with `with_pythonic_errors()` to translate StatusCode to domain exceptions:

```python
# tilebox/datasets/sync/client.py:26-29
dataset_service_stub = with_pythonic_errors(DatasetServiceStub(channel))
collection_service_stub = with_pythonic_errors(CollectionServiceStub(channel))
data_access_service_stub = with_pythonic_errors(DataAccessServiceStub(channel))
data_ingestion_service_stub = with_pythonic_errors(DataIngestionServiceStub(channel))
```

Error mapping includes (_tilebox/grpc/error.py:12-37):
- `StatusCode.NOT_FOUND` → `NotFoundError(KeyError)`
- `StatusCode.INVALID_ARGUMENT` → `ArgumentError(ValueError)`
- `StatusCode.UNAUTHENTICATED` → `AuthenticationError(IOError)`
- `StatusCode.PERMISSION_DENIED` → `AuthenticationError(IOError)`

### AnyMessage and Type URL Pattern
Data is transported as type-erased messages with type URLs:

```python
# tilebox/datasets/data/datapoint.py:14-28
@dataclass(frozen=True)
class AnyMessage:
    type_url: str
    value: bytes

    @classmethod
    def from_message(cls, a: core_pb2.Any) -> "AnyMessage":
        return cls(type_url=a.type_url, value=a.value)
```

`RepeatedAny` optimizes for lists of same-type messages by avoiding type_url repetition (lines 30-43).

### Message Pool and Dynamic Registration
Type URLs are resolved to Python classes via a global message pool:

```python
# tilebox/datasets/message_pool.py:28-29
def get_message_type(type_url: str) -> type:
    return GetMessageClass(Default().FindMessageTypeByName(type_url))
```

Message types can be registered dynamically from descriptor sets (lines 12-21).

### Protobuf to xarray Conversion with Type Inference
The conversion system dynamically creates field converters based on protobuf field descriptors:

```python
# tilebox/datasets/protobuf_conversion/protobuf_xarray.py:26-49
class MessageToXarrayConverter:
    def __init__(self, initial_capacity: int = 0) -> None:
        self._capacity = initial_capacity
        self.count = 0
        self._converters: dict[str, _FieldConverter] | None = None

    def convert(self, message: Message) -> None:
        self._ensure_capacity(self.count + 1)
        if self._converters is None:
            self._converters = _create_field_converters(message, buffer_size=self._capacity)
        self._convert(message)
```

Specialized field types handle well-known protobuf types (field_types.py:239-248):
- `google.protobuf.Timestamp` → `TimestampField()` → `datetime64[ns]`
- `google.protobuf.Duration` → `TimeDeltaField()` → `timedelta64[ns]`
- `datasets.v1.UUID` → `UUIDField()` → string
- `datasets.v1.Geometry` → `GeometryField()` → shapely geometry
- `datasets.v1.LatLon` → `LatLonField()` → 2D coordinates with dimension metadata

Multi-dimensional fields use `value_dim_meta` for coordinate naming:
```python
# field_types.py:214
super().__init__(float, value_dim=3, value_dim_meta=("lat_lon_alt", ["latitude", "longitude", "altitude"]))
```

### Client Construction and Authentication
Sync client construction follows a layered pattern with error wrapping:

```python
# tilebox/datasets/sync/client.py:25-33
channel = open_channel(url, token_from_env(url, token))
dataset_service_stub = with_pythonic_errors(DatasetServiceStub(channel))
# ... other stubs wrapped similarly
service = TileboxDatasetService(dataset_service_stub, collection_service_stub, ...)
self._client = BaseClient(service)
```

Token resolution with environment fallback (client.py:67-77):
```python
def token_from_env(url: str, token: str | None) -> str | None:
    if token is None:
        token = os.environ.get("TILEBOX_API_KEY", None)

    if token is None and parse_channel_info(url).address == "api.tilebox.com":
        raise ValueError("No API key provided and no TILEBOX_API_KEY environment variable set...")
```

### Pagination with Progress Tracking
Query results are paginated with optional progress bars (sync/pagination.py:15-49):
- `with_progressbar()` shows datapoint count progress
- `with_time_progressbar()` estimates progress based on time intervals
- `with_time_progress_callback()` provides programmatic progress updates

The pattern handles single-page responses by skipping progress UI entirely.

### Spatial Filtering with Shapely Integration
Spatial filters support geometry types, modes, and coordinate systems:

```python
# tilebox/datasets/data/data_access.py:42-56
@dataclass(frozen=True)
class SpatialFilter:
    geometry: Geometry
    mode: SpatialFilterMode = SpatialFilterMode.INTERSECTS
    coordinate_system: SpatialCoordinateSystem = SpatialCoordinateSystem.SPHERICAL
```

Modes and coordinate systems map to protobuf enums (lines 15-30):
- `SpatialFilterMode.INTERSECTS` / `SpatialFilterMode.CONTAINS`
- `SpatialCoordinateSystem.CARTESIAN` / `SpatialCoordinateSystem.SPHERICAL`

Geometry transport uses shapely WKB serialization (lines 65-75):
```python
def to_message(self) -> data_access_pb2.SpatialFilter:
    return data_access_pb2.SpatialFilter(
        geometry=well_known_types_pb2.Geometry(wkb=to_wkb(self.geometry)),
        mode=self.mode.value,
        coordinate_system=self.coordinate_system.value,
    )
```

Flexible parsing accepts shapely Geometry objects directly or dict format with string mode/coordinate_system values (lines 84-102).

### Producer/Consumer Concurrency for Data Processing
Query result processing uses threaded producer/consumer pattern to overlap network I/O with protobuf decoding:

```python
# tilebox/datasets/sync/dataset.py:492-496
# lets parse the incoming pages already while we wait for the next page from the server
# we solve this using a classic producer/consumer with a queue of pages for communication
# this would also account for the case where the server sends pages faster than we are converting
# them to xarray
concurrent_producer_consumer(pages, convert_page)
```

The producer/consumer implementation uses threading with bounded queue (_tilebox/grpc/producer_consumer.py:9-35):
- Producer (main thread): fetches pages from gRPC service, puts in queue
- Consumer (background thread): converts protobuf messages to xarray format
- Queue buffer (default 10 pages): handles rate mismatches between network and CPU

Pattern ensures continuous processing: while consumer converts page N, producer fetches page N+1.

### Async/Sync Client Unification
Both sync and async clients share the same service layer and base client, differing only in Promise resolution:

```python
# tilebox/datasets/sync/client.py:35-36
def datasets(self) -> Group:
    return self._client.datasets(DatasetClient).get()

# tilebox/datasets/aio/client.py:35-36
async def datasets(self) -> Group:
    return await self._client.datasets(DatasetClient)
```

Both clients use identical construction patterns with error-wrapped stubs (sync/client.py:26-29, aio/client.py:26-29):
- Same service layer (`TileboxDatasetService`)
- Same base client (`BaseClient`)
- Same error wrapping (`with_pythonic_errors`)
- Only difference: sync uses `open_channel`, async uses `aio.open_channel`

### Time Interval Flexible Parsing
TimeInterval supports multiple input formats with automatic timezone handling:

```python
# tilebox/datasets/query/time_interval.py:17-19
TimeIntervalLike: TypeAlias = (
    DatetimeScalar | tuple[DatetimeScalar, DatetimeScalar] | xr.DataArray | xr.Dataset | "TimeInterval"
)
```

Timezone-naive datetimes automatically converted to UTC (lines 44-48):
```python
if self.start.tzinfo is None:
    object.__setattr__(self, "start", self.start.replace(tzinfo=timezone.utc))
```

Supports half-open intervals `[start, end)` as default behavior (lines 32-35).

### Data Ingestion with Type Inference
Ingestion accepts multiple data formats with automatic conversion to protobuf messages:

```python
# tilebox/datasets/protobuf_conversion/to_protobuf.py:18-19
IngestionData = Mapping[str, Collection[Any]] | Iterable[tuple[str, Collection[Any]]] | pd.DataFrame | xr.Dataset
DatapointIDs = pd.DataFrame | pd.Series | xr.Dataset | xr.DataArray | np.ndarray | Collection[UUID] | Collection[str]
```

Non-DataFrame/Dataset inputs automatically converted to pandas DataFrame (lines 28-32):
```python
if not isinstance(data, xr.Dataset) and not isinstance(data, pd.DataFrame):
    try:
        data = pd.DataFrame(data)
    except (TypeError, ValueError) as err:
        raise ValueError(f"Invalid ingestion data. Failed to convert data to a pandas.DataFrame(): {err}")
```

Field validation ensures all fields match protobuf message schema and have consistent lengths (lines 43-50).

### UUID Handling with Nil Support
UUID conversion handles nil UUIDs and optional fields:

```python
# tilebox/datasets/uuid.py:10-13
def uuid_message_to_uuid(uuid_message: id_pb2.ID | well_known_types_pb2.UUID) -> UUID:
    if uuid_message.uuid == b"":
        return _NIL_UUID
    return UUID(bytes=uuid_message.uuid)
```

Separate functions for optional UUIDs (lines 16-21) and required UUIDs with nil validation (lines 30-33).

## Detailed Patterns: Tilebox Workflows (Documentation)

### Task Definition and Execution Model
Tasks are the smallest unit of work, defined as classes extending the Task base class:
- Source: https://docs.tilebox.com/workflows/concepts/tasks
- Pattern: `class MyTask(Task): def execute(self, context: ExecutionContext):`
- Tasks automatically become dataclasses for input parameter definition
- Task identifiers default to class name but can be overridden for production use

### Task Composition and Subtask Orchestration
Tasks can submit other tasks as subtasks for modular workflow design:
- Pattern: `context.submit_subtask(ChildTask(parameters))`
- Subtask execution is automatically parallelized when possible
- Limit of 64 subtasks per task to discourage performance bottlenecks
- Recursive subtask submission enables MapReduce-style patterns

### Dependency Management and Execution Order
Tasks can express dependencies on other tasks:
- Pattern: `context.submit_subtask(TaskB(), depends_on=[task_a])`
- Dependent tasks execute only after dependencies complete successfully
- If dependent task submits subtasks, those must also complete before dependents run
- Enables sequential and parallel execution patterns within same workflow

### Retry Handling and Fault Tolerance
Built-in retry mechanism for handling transient failures:
- Pattern: `context.submit_subtask(FlakyTask(), max_retries=5)`
- Failed tasks can be picked up by any available runner
- Useful for tasks dependent on external services

### Semantic Versioning and Task Evolution
Tasks support versioning for production deployment management:
- Pattern: `@staticmethod def identifier() -> tuple[str, str]: return "namespace/task", "v1.3"`
- Version format: vX.Y (major.minor)
- Compatible execution: matching major version, runner minor >= submitted minor
- Enables gradual rollout and coexistence of multiple versions

### Task States and Lifecycle
Tasks progress through defined states:
- QUEUED → RUNNING → COMPUTED (success) or FAILED or CANCELLED
- COMPUTED state is terminal and immutable
- State transitions managed by workflow orchestrator

## Detailed Patterns: Terrafloww Platform Code (Thoroughly Verified)

### Processing Engine Service Architecture
**Verified Implementation:** Dual-server architecture with concurrent gRPC and Apache Arrow Flight servers.

<augment_code_snippet path="services/processing_engine/app/main.py" mode="EXCERPT">
````python
async def serve():
    """Starts BOTH gRPC and Flight servers concurrently."""
    grpc_port = os.environ.get("GRPC_PORT", "50051")
    grpc_server = grpc.aio.server()

    flight_port = os.environ.get("FLIGHT_PORT", "50052")
    flight_location = f"grpc+tcp://0.0.0.0:{flight_port}"
    flight_server_instance = FlightServer(location=flight_location)
````
</augment_code_snippet>

**Key patterns verified:**
- Concurrent async servers using `asyncio.run_in_executor()` for Flight's blocking `serve()` call
- Environment-based port configuration (GRPC_PORT=50051, FLIGHT_PORT=50052)
- gRPC uses `grpc.aio` for async operations, Flight uses thread executor for concurrency

### Fire-and-Forget gRPC Execution Pattern
**Verified Implementation:** Async gRPC service with immediate response and background Ray execution.

<augment_code_snippet path="services/processing_engine/app/grpc_service.py" mode="EXCERPT">
````python
class ProcessingEngineServicer(processing_engine_pb2_grpc.ProcessingEngineServiceServicer):
    async def ExecuteWorkflow(self, request, context):
        execution_id = request.job_id if request.job_id else str(uuid.uuid4())
        flight_ticket_str = f"ticket_for_{execution_id}"

        # Fire-and-forget background execution
        asyncio.create_task(run_workflow_entrypoint(execution_id, request.plan))

        return processing_engine_pb2.ExecuteWorkflowResponse(
            execution_id=execution_id,
            flight_ticket=flight_ticket_str,
            status_message="Workflow submitted for background execution."
        )
````
</augment_code_snippet>

**Key patterns verified:**
- Immediate response with execution_id and flight_ticket
- Background Ray execution via `asyncio.create_task()`
- Error handling with `await context.abort(grpc.StatusCode.INTERNAL, message)`

### Flight Server Execution State Management
**Verified Implementation:** Thread-safe execution state with schema evolution and completion signaling.

<augment_code_snippet path="services/processing_engine/app/flight_server.py" mode="EXCERPT">
````python
class ExecutionState:
    def __init__(self, execution_id: str):
        self.execution_id = execution_id
        self.batches: List[pa.RecordBatch] = []
        self.schema: Optional[pa.Schema] = None
        self.completion_event = threading.Event()
        self.status = "RUNNING"
        self.created_at = time.time()
````
</augment_code_snippet>

**Key patterns verified:**
- Thread-safe operations using `threading.RLock()` for nested operations
- Schema evolution via `pa.unify_schemas()` for heterogeneous batch types
- Completion signaling using `threading.Event.wait(timeout=120.0)`
- TTL-based cleanup with background thread (default 1 hour TTL)
- Instance-level state management (no global caches)

### Hilbert Curve Spatial Locality Optimization
**Verified Implementation:** Complete Hilbert curve implementation for spatial tile ordering.

<augment_code_snippet path="libs/tfw_engine_core/src/terrafloww/engine_core/spatial_utils.py" mode="EXCERPT">
````python
def hilbert_index(tile_r: int, tile_c: int, order: int = 16) -> int:
    """
    Compute Hilbert curve index for spatial tile coordinates.

    The Hilbert curve is a space-filling curve that maps 2D coordinates
    to a 1D index while preserving spatial locality.
    """
    try:
        max_coord = 2 ** order
        if tile_r < 0 or tile_c < 0 or tile_r >= max_coord or tile_c >= max_coord:
            tile_r = max(0, min(tile_r, max_coord - 1))
            tile_c = max(0, min(tile_c, max_coord - 1))

        return _hilbert_xy_to_index(tile_r, tile_c, order)
    except Exception as e:
        # Fallback to row-major ordering
        return tile_r * 1000 + tile_c
````
</augment_code_snippet>

**Key patterns verified:**
- Recursive bit manipulation algorithm in `_hilbert_xy_to_index()` (lines 59-94)
- Coordinate bounds checking and clamping for safety
- Fallback to row-major ordering on computation errors
- Integration with planner for spatial locality optimization (planner.py:482-521)
- Scene-aware sorting with `hilbert_index_with_scene()` for temporal diversity

### Ray Driver Orchestration with Chunky Batching
**Verified Implementation:** Ray driver with singleton pattern and HTTP actor pool integration.

<augment_code_snippet path="libs/tfw_engine_core/src/terrafloww/engine_core/runtime_ray/driver.py" mode="EXCERPT">
````python
class RayDriver:
    def __init__(self):
        self.output_storage_path = os.environ.get("TFW_OUTPUT_STORAGE_PATH", "/tmp/terrafloww/results")
        self.http_actor_pool = None

    async def _ensure_ray_initialized(self, address: str):
        """Ensure Ray is initialized and HTTP actor pool is ready."""
        if not ray.is_initialized():
            ray.init(address=address, ignore_reinit_error=True)

        # Initialize HTTP actor pool for connection pooling optimization
        await self._initialize_http_pool()
````
</augment_code_snippet>

**Key patterns verified:**
- Singleton driver instance with async lock for thread safety
- Ray initialization with `RAY_ADDRESS` environment variable (default "auto")
- HTTP actor pool initialization for connection reuse optimization
- Chunky batching: groups WindowSpecs by spatial window (scene_id, tile_r, tile_c)
- Hilbert curve sorting within each scene for spatial locality
- Temporal diversity via round-robin interleaving across scenes

### HTTP Actor Pool for Connection Persistence
**Verified Implementation:** Complete Ray actor pool with persistent aiohttp sessions.

<augment_code_snippet path="libs/tfw_engine_core/src/terrafloww/engine_core/runtime_ray/actor_pool.py" mode="EXCERPT">
````python
@ray.remote
class HTTPActorPool:
    def __init__(self, pool_size: int = 4, max_connections_per_actor: int = 25):
        self.pool_size = pool_size
        self.max_connections_per_actor = max_connections_per_actor
        self.actors: List[ray.ObjectRef] = []
        self.round_robin_index = 0
        self.actor_stats = {}

    async def initialize_pool(self):
        """Create named actors with detached lifetime for persistence."""
        for i in range(self.pool_size):
            actor_name = f"http_connection_pool_{i}"
            actor = HTTPConnectionPool.options(
                name=actor_name,
                lifetime="detached",
                max_restarts=3,
                max_task_retries=2
            ).remote(pool_size=1, max_connections=self.max_connections_per_actor)
````
</augment_code_snippet>

**Key patterns verified:**
- Named Ray actors with detached lifetime for persistence across driver restarts
- Round-robin distribution with `get_next_actor()` for load balancing
- Fault tolerance: max_restarts=3, max_task_retries=2
- Actor health checking and recreation on failures
- Integration with HTTPConnectionPool actors using persistent aiohttp sessions

### HTTPConnectionPool Ray Actor Implementation
**Verified Implementation:** Persistent aiohttp sessions with optimal configuration.

<augment_code_snippet path="libs/tfw_engine_core/src/terrafloww/engine_core/runtime_ray/http_pool.py" mode="EXCERPT">
````python
@ray.remote
class HTTPConnectionPool:
    def __init__(self, pool_size: int = 4, max_connections: int = 100):
        self.pool_size = pool_size
        self.max_connections = max_connections
        self.sessions: List[aiohttp.ClientSession] = []

    async def initialize(self):
        """Initialize persistent aiohttp ClientSessions with optimal configuration."""
        connections_per_session = self.max_connections // self.pool_size

        for i in range(self.pool_size):
            connector = aiohttp.TCPConnector(
                limit=connections_per_session,
                limit_per_host=min(50, connections_per_session),
                keepalive_timeout=300,  # 5 minutes
                enable_cleanup_closed=True,
                use_dns_cache=True
            )
````
</augment_code_snippet>

**Key patterns verified:**
- Persistent aiohttp.ClientSession with TCPConnector optimization
- Connection pooling: 100 total connections, 50 per host, 5-minute keepalive
- Round-robin session selection for load balancing
- Range request grouping by URL for connection reuse
- Performance tracking: requests_processed, bytes_fetched, connection_reuses

### Kernel Registry and Function Dispatch
**Verified Implementation:** Decorator-based function registry with dynamic dispatch.

<augment_code_snippet path="libs/tfw_engine_core/src/terrafloww/engine_core/registry.py" mode="EXCERPT">
````python
FUNCTIONS: dict[str, callable] = {}

def register(name: str):
    """Register a function with the given name."""
    def _decor(fn: callable):
        FUNCTIONS[name] = fn
        logger.info(f"Registered function '{name}' with signature: {fn.__name__}{fn.__annotations__}")
        return fn
    return _decor

def get(name: str) -> callable:
    """Get a registered function by name."""
    if name not in FUNCTIONS:
        raise KeyError(f"Function '{name}' not found in registry")
    return FUNCTIONS[name]
````
</augment_code_snippet>

**Key patterns verified:**
- Global function registry with decorator-based registration
- Dynamic function dispatch by string identifier
- NDVI kernel registered as `@register("terrafloww.spectral.ndvi")`
- Worker imports process.py to trigger registration via import side effects
- Error handling with detailed function signature logging

### Ray Worker Implementation and WindowSpec Processing
**Verified Implementation:** Ray remote worker with COG fetching and kernel execution.

<augment_code_snippet path="libs/tfw_engine_core/src/terrafloww/engine_core/runtime_ray/worker.py" mode="EXCERPT">
````python
@ray.remote(**DEFAULT_WORKER_RESOURCES)
def process_batch_on_worker(
    window_specs_batch: List[WindowSpec],
    plan_apply_steps_serializable: List[Dict[str, Any]],
    execution_id: str,
    flight_address: str
) -> bool:
    """Ray remote task to process a batch of window specs and write to Flight server."""

    # Check if HTTP actor pool is enabled
    use_http_pool = os.environ.get("TFW_USE_HTTP_POOL", "false").lower() == "true"

    if use_http_pool:
        raw_bytes_map = await _fetch_with_actor_pool(window_specs_batch)
    else:
        raw_bytes_map = await _fetch_with_httpx_client(window_specs_batch)
````
</augment_code_snippet>

**Key patterns verified:**
- Ray remote function with configurable resources (DEFAULT_WORKER_RESOURCES)
- Feature flag for HTTP actor pool vs httpx client (`TFW_USE_HTTP_POOL`)
- Batch processing of WindowSpec objects for spatial locality
- Flight server integration for result streaming
- Error handling with boolean return for success/failure

### WindowSpec Data Structure
**Verified Implementation:** Immutable dataclass for work unit specification.

<augment_code_snippet path="libs/tfw_engine_core/src/terrafloww/engine_core/runtime_ray/common_types.py" mode="EXCERPT">
````python
@dataclass(frozen=True)
class WindowSpec:
    """Specifies a single window of data to be fetched and processed by a worker."""
    # Identification & Source
    cog_href: str
    scene_id: str
    catalog_band_name: str
    operation_band_name: str
    collection: str

    # Fetching Parameters
    byte_offset: int
    byte_size: int

    # Window Geometry (Pixel Coordinates)
    window_col_off: int
    window_row_off: int
    window_width: int
    window_height: int

    # Tile Context
    tile_r: int
    tile_c: int
    tile_shape_decode_h: int
    tile_shape_decode_w: int
````
</augment_code_snippet>

**Key patterns verified:**
- Immutable frozen dataclass for thread safety and hashability
- Complete specification for COG tile fetching (byte_offset, byte_size)
- Spatial context (tile_r, tile_c) for grouping and Hilbert sorting
- CRS and transform information for geo-referencing
- Processing parameters (dtype_str, predictor, scale_factor, offset_factor)

## Transport and Error Handling

- Tilebox:
  - gRPC stubs for DatasetService/DataAccess/DataIngestion/CollectionService
  - Promise-based composition in sync paths; explicit error wrapping for ArgumentError/NotFoundError; environment introspection for ClientInfo (package versions)
  - Token via TILEBOX_API_KEY; default API url api.tilebox.com; channel helpers (_tilebox.grpc)
- Terrafloww:
  - gRPC service ExecuteWorkflow (async) returning execution_id + Flight ticket quickly; background Ray run
  - Arrow Flight server do_get serves result tables; client polls do_get ticket until available
  - Targets via env: TFW_PROCESSING_GRPC_TARGET / TFW_PROCESSING_FLIGHT_TARGET

## Server/Execution Plane

- Tilebox Workflows (from docs):
  - Task Runners: long-running agents; register tasks; run_forever; clusters for isolation; heartbeats, retries, idempotency
  - Distributed execution across varying hardware profiles (network/GPU), cluster selection per subtask
- Terrafloww Processing Engine:
  - Ray cluster on K8s (autoscaling); driver plans and submits chunky tasks; workers fetch COGs and compute kernels
  - Spatial task ordering via Hilbert curve; windowing via WindowSpec; HTTP actor pool for high-latency I/O
  - Flight internal service address used by workers to stream to the serving plane

## Data-access vs Processing

- Tilebox: Datasets emphasizes read and write (ingest/delete) and exposes time/spatial query primitives; workflows add general task orchestration beyond dataset access
- Terrafloww: Surface is currently processing-first (server-side operators like ndvi); ingestion path handled by separate services (metadata/catalog) and scripts; read path derives from the plan

## Deployment and Ops

- Tilebox: Runners can be deployed on-prem or cloud; clusters optional; scaling via cloud autoscaling; observability guidance (OTel, logging)
- Terrafloww: K8s manifests for Ray clusters; dedicated processing_engine service with async gRPC and Arrow Flight; environment-driven configuration; planned GPU-direct streaming

## Strengths and Trade-offs

- Tilebox strengths
  - Clean dataset/collection abstraction; xarray-first ergonomics for scientists
  - Ingestion/deletion lifecycle integrated
  - General-purpose workflow orchestrator with runners, dependencies, clusters
- Tilebox trade-offs
  - Client-side conversion cost from protobuf to xarray
  - Message pool/type_url indirection requires message registration and schema alignment

- Terrafloww strengths
  - Arrow-first end-to-end; Flight enables zero-copy-ish streaming to clients
  - Ray planner/driver model favors high throughput with coarse-grained tasks and spatial locality
  - Operator model keeps complex kernels server-side and versioned
- Terrafloww trade-offs
  - Current public SDK intentionally thin; fewer high-level dataset CRUD operations exposed
  - Orchestration is focused on the Processing Engine rather than a general task runner framework

## API Examples (abridged)

Tilebox — dataset access (sync):

```python
from tilebox.datasets import Client
c = Client()
ds = c.dataset("open_data.copernicus.sentinel1_sar").get()
coll = ds.collection("default")
xr_ds = coll.query(temporal_extent=("2024-01-01","2024-02-01"), spatial_extent=None)
```

Terrafloww — workflow DAG:

```python
    import re
    import terrafloww as tfw
    from shapely.geometry import box

    # Create AOI for California region (San Francisco Bay Area)
    california_aoi = box(-122.5, 37.7, -122.3, 37.9)

    # Load data using public SDK
    collection = tfw.load(
        "sentinel-2-l2a",
        bands=["red", "nir"],  # Using common names that match the actual data
        aoi=california_aoi,  # California region (San Francisco Bay Area)
        datetime="2024-06-01/2024-06-23",  # June 2024 timeframe
        processing_engine_grpc_target=grpc_target,
        processing_engine_flight_target=flight_target
    )

    # Apply NDVI operation using public SDK
    # Use the full function ID and correct parameter names (matching internal implementation)
    ndvi_collection = collection.apply("terrafloww.spectral.ndvi", {"red_band": "red", "nir_band": "nir"})
    
    print(" NDVI operation added successfully")

    # Apply head limit to get exactly 2 unique spatial windows
    limited_collection = ndvi_collection.head(2)
    
    print(" Head limit applied successfully")

    # Execute computation
    print(" Executing workflow on platform...")
    result = limited_collection.compute()

    # Verify results
    print(f"Result table shape: {result.num_rows} rows, {len(result.column_names)} columns")
    print(f"Columns: {result.column_names}")

```

## Terminology Mapping

- Tilebox Dataset ↔ Terrafloww Collection in LOAD step
- Tilebox Collection ↔ Terrafloww logical subset/stream within dataset windows
- Tilebox QueryResultPage (RepeatedAny) ↔ Terrafloww RecordBatch pages via Flight
- Tilebox Task/Job/Runner ↔ Terrafloww Ray driver/worker jobs within Processing Engine

## Gaps & Opportunities (for Terrafloww)

1. Dataset-centric API surface (optional layer)
   - Provide a thin convenience layer mirroring: dataset.collections(), collection.query(), find(id), ingest/delete
   - Backed by the existing Processing Engine and Arrow Flight for reads; ingestion routed to metadata/catalog
2. xarray interop utilities
   - Optional function to convert Arrow tables (raster schema) to xarray for parity with Tilebox user expectations
3. Task orchestration
   - Evaluate exposing a generalized job/task API (or documented patterns) for multi-stage workflows outside raster kernels
4. Message schema registry
   - While Terrafloww is Arrow-first, document schema stability/versioning; add schema discovery in Flight descriptors

## Security & Auth

- Tilebox: token via TILEBOX_API_KEY; channel helpers enforce requirement when connecting to api.tilebox.com
- Terrafloww: environment-configured endpoints; recommend API key/metadata headers for gRPC and Flight; document in SDK README

## Performance Considerations

- Tilebox: producer/consumer concurrency for protobuf-to-xarray conversion while fetching next page; memory bounded by queue buffer size
- Terrafloww: spatial windowing, Hilbert curve ordering, batched ingestion of results to Flight cache; coarse-grained tasks to minimize Ray scheduling overhead

## What to Adopt (Short Term)

- Document dataset-centric convenience Facade in SDK (without breaking current DAG semantics)
- Add Arrow→xarray conversion helper for scientists
- Extend README with explicit auth examples and failure-handling patterns mirroring Tilebox docs (e.g., retries, timeouts)

## References

- Tilebox Docs (Introduction, Datasets, Workflows/Task Runners): https://docs.tilebox.com/
- Terrafloww platform docs and code in this repo (see README and docs/*)

